import json
import requests
from fastapi import APIRouter, Request
from fastapi.responses import StreamingResponse
import mysql.connector
from contextlib import contextmanager
from openai import OpenAI
router = APIRouter()

client = OpenAI(
    # 如果没有配置环境变量，请用百炼API Key替换：api_key="sk-xxx"
    #api_key='sk-4b784c92b11c4ce5916b075b24b04953',
    api_key='sk-8O7LaJ7d4YP8JuPN16Fa330664924811A10aF4Ad1eFeA1Cc',
    #base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
    base_url="https://one-api.igancao.cn/v1"
)


def get_access_token(ak, sk):
    auth_url = "https://aip.baidubce.com/oauth/2.0/token"
    resp = requests.get(auth_url, params={"grant_type": "client_credentials", "client_id": ak, 'client_secret': sk})
    return resp.json().get("access_token")


def get_stream_response(prompt):
    ak = "kpRcOS2eFFDAUdtkkZEdzntq"
    sk = "EqPBhYmKGheWxSUiA9IWDvqxVMVMirp6"
    source = "&sourceVer=0.0.1&source=app_center&appName=streamDemo"
    #base_url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions"
    #base_url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-speed-128k"
    base_url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro"

    url = base_url + "?access_token=" + get_access_token(ak, sk) + source
    data = {
        "messages": [{"role": "user", "content": prompt}],
        "stream": True
    }
    payload = json.dumps(data)
    headers = {'Content-Type': 'application/json'}
    return requests.post(url, headers=headers, data=payload, stream=True)


def gen_stream_deepseek(prompt, id, conversationsId):
    """
       调用 deepseek 流式接口生成回复，并实时 yield 返回内容，增加思考过程的输出。

       :param prompt: 用户输入的提示文本
       :param id: 消息 id，用于保存消息（不为 0 时保存）
       :param conversationsId: 会话 id，用于保存对话（不为 0 时保存）
    """
    full_response = ""
    reasoning_content = ""  # 完整思考过程
    answer_content = ""  # 完整回复内容
    is_answering = False  # 判断是否结束思考过程并开始回复
    think_started = False  # 标记思考过程是否开始

    # 创建聊天完成请求，启用流式返回
    stream = client.chat.completions.create(
        model="deepseek-r1",  # 根据需要可更换模型名称
        messages=[{"role": "user", "content": prompt}],
        stream=True
    )

    # 遍历每个返回的 chunk
    for chunk in stream:
        # 如果 chunk 中不包含 choices（例如返回 token 使用情况），则跳过
        if not getattr(chunk, 'choices', None):
            continue

        delta = chunk.choices[0].delta
        text = ""

        # 检查是否有 reasoning_content 属性
        if getattr(delta, 'reasoning_content', None):
            if not think_started:  # 如果是第一次遇到思考过程，添加 <think> 标签
                text = "<think>"
                think_started = True
            text += delta.reasoning_content  # 累积思考过程的内容
            reasoning_content += delta.reasoning_content
        elif getattr(delta, 'content', None):
            if think_started:  # 如果思考过程已经开始，添加 </think> 标签
                text += "</think>"
                think_started = False  # 重置标志，结束思考过程
            if not is_answering:
                print("\n" + "=" * 20 + "完整回复" + "=" * 20 + "\n")
                is_answering = True
            text += delta.content
            answer_content += delta.content
        else:
            continue

        full_response += text
        yield text

    # 回复结束后保存完整回复（若 id 或 conversationsId 不为 0）
    if id != 0:
        save_msg(id, full_response)
    if conversationsId != 0:
        save_conversation(conversationsId, full_response)

@contextmanager
def get_db_connection():
    # db = mysql.connector.connect(
    #     host="rm-bp14dy1c89k64zb7cpo.mysql.rds.aliyuncs.com",
    #     user="gancaocn",
    #     password="gancaocn2020#@!",
    #     database="tcmai"
    # )
    db = mysql.connector.connect(
        host="pc-bp1kdf6hyxei6b6zv.rwlb.rds.aliyuncs.com",
        user="xuanqi",
        password="@SU@tBywzb6vSqY",
        database="gc_tcmai"
    )
    cursor = db.cursor()
    try:
        yield cursor  # 在这里生成一个游标对象供调用者使用
    finally:
        db.commit()  # 提交事务
        cursor.close()  # 关闭游标
        db.close()  # 关闭数据库连接

def save_conversation(conversationsId, content):
    with get_db_connection() as cursor:
        # Check if the answer is empty
        check_sql = "SELECT id ,answer  FROM activity_conversations WHERE id = %s"
        cursor.execute(check_sql, (conversationsId,))
        result = cursor.fetchone()

        if result and (result[1] is None or result[1] == ""):  # If answer is empty
            # Insert into activity_consulting_msg table
            update_sql = "UPDATE activity_conversations SET answer = %s WHERE id = %s"
            cursor.execute(update_sql, (content, conversationsId))

def save_msg(id, content):
    with get_db_connection() as cursor:
        # Insert into activity_consulting_msg table
        sql = "UPDATE activity_consulting_msg SET answer = %s WHERE id = %s"
        val = (content, id)
        cursor.execute(sql, val)

def extract_result_from_chunk(chunk):
    try:
        chunk_data = json.loads(chunk)
        return chunk_data.get("result", "")
    except json.JSONDecodeError:
        return ""

def gen_stream(prompt, id,conversationsId):
    response = get_stream_response(prompt)
    full_response = ""
    for chunk in response.iter_lines():
        chunk = chunk.decode("utf8")
        if chunk.startswith("data:"):
            chunk = chunk[5:]

        text= extract_result_from_chunk(chunk)
        full_response += text

        yield text
    if id != 0:
        save_msg(id, full_response)
    if conversationsId != 0:
        save_conversation(conversationsId,full_response)



@router.post("/eb_stream")
async def eb_stream(request: Request):
    result1 = await request.json()

    state_dict = result1["keys"]
    question = state_dict["question"]
    documents = state_dict["documents"]
    raw_question = state_dict["raw_question"]
    previous_question = state_dict["previous_q"]
    previous_answer = state_dict["previous_a"]
    id = result1.get("id", 0)
    conversationsId = result1.get("conversationsId", 0)
    #限制每本书长度
    documents = [doc[:500] for doc in documents]


    prompt = f'''
    你的名字是轩岐问对AI助手，由浙江中医药大学与甘草医生共同研发，擅长回答与中医相关的问题
    这是上一轮的问题：{previous_question}
    这是上一轮的回答：{previous_answer}
    这是本轮输入的问题：{raw_question}
    这是参考的知识条文：{documents}
    请参考知识条文，先筛选出对本轮问题有帮助的条文，然后根据条文回答提出的问题
    要求：
    1.回答的内容与问题相关；
    2.回答越详细越好；
    3.如果知识条文与本轮问题不相关，不参考知识条文，直接回答本轮问题
    4.注意回答的语义通顺
    5.回答的内容请直接解答问题，不要出现根据提供的知识条文回答等字眼
    6.在返回思考过程中尽量精简条文内容 不要全部展示
    请现在给出你的回答：
    '''
    return StreamingResponse(gen_stream_deepseek(prompt, id,conversationsId))
